<?php
// File: /admin/categories.php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// 1) Load config & auth
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';
authenticate_admin();

// 2) Fetch all categories
try {
    $stmt       = $pdo->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $categories = [];
    $loadError  = $e->getMessage();
}

// 3) Render page
?>
<?php include __DIR__ . '/../includes/header.php'; ?>

<main class="container">

  <h1 class="section-title">
    <i class="fas fa-tags"></i> Category Management
  </h1>

  <?php if (!empty($loadError)): ?>
    <div class="alert alert-danger">
      Error loading categories: <?= htmlspecialchars($loadError) ?>
    </div>
  <?php endif; ?>

  <!-- Add New Category -->
  <section class="card mb-4">
    <div class="card-header">
      <h2><i class="fas fa-plus-circle"></i> Add New Category</h2>
    </div>
    <form id="category-form" action="javascript:;" class="card-body">
      <div class="form-group">
        <label for="category-name">Category Name</label>
        <input type="text"
               id="category-name"
               name="name"
               placeholder="Enter category name"
               required />
      </div>
      <div class="form-group">
        <label for="category-description">Description</label>
        <textarea id="category-description"
                  name="description"
                  placeholder="Enter description"></textarea>
      </div>
      <div class="form-group">
        <label class="checkbox-label">
          <input type="checkbox"
                 id="include-in-cost"
                 name="include_in_cost"
                 checked />
          <span class="checkmark"></span>
          Include this category in cost calculations
        </label>
        <small class="form-help">
          Uncheck this if the category should only be included when linked with a multiplier category
        </small>
      </div>
      <button type="submit" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add Category
      </button>
    </form>
  </section>

  <!-- Existing Categories -->
  <h2 class="section-subtitle">
    <i class="fas fa-list"></i> Existing Categories
  </h2>

  <div class="dashboard-grid">
    <?php if (empty($categories)): ?>
      <div class="card">
        <div class="card-body">
          <p>No categories found. Add one above to get started.</p>
        </div>
      </div>
    <?php else: ?>
      <?php foreach ($categories as $cat): ?>
        <div class="card category-block" data-category-id="<?= $cat['id'] ?>">
          <div class="card-header">
            <h3>
              <?= htmlspecialchars($cat['name']) ?>
              <?php if (!$cat['include_in_cost']): ?>
                <span class="badge badge-warning" title="Excluded from cost calculations unless linked">
                  <i class="fas fa-link"></i> Conditional
                </span>
              <?php endif; ?>
            </h3>
            <button class="delete-category-btn"
                    data-id="<?= $cat['id'] ?>"
                    title="Delete Category">
              <i class="fas fa-trash"></i>
            </button>
          </div>

          <?php if ($cat['description']): ?>
            <div class="card-body">
              <p><?= nl2br(htmlspecialchars($cat['description'])) ?></p>
              <?php if (!$cat['include_in_cost']): ?>
                <p class="text-warning">
                  <i class="fas fa-info-circle"></i>
                  This category is excluded from cost calculations unless linked with a multiplier category.
                </p>
              <?php endif; ?>
            </div>
          <?php endif; ?>

          <div class="card-footer">
            <h4><i class="fas fa-layer-group"></i> Subcategories</h4>
            <ul id="sub-list-<?= $cat['id'] ?>" class="subcategory-list">
              <!-- populated by JS -->
            </ul>

            <form class="subcategory-form mt-3"
                  data-category-id="<?= $cat['id'] ?>"
                  action="javascript:;">
              <div class="form-group">
                <input type="text"
                       name="name"
                       placeholder="Subcategory name"
                       required />
              </div>
              <div class="form-group">
                <textarea name="description"
                          placeholder="Description"></textarea>
              </div>
              <button type="submit" class="btn btn-sm">
                <i class="fas fa-plus"></i> Add Subcategory
              </button>
            </form>
          </div>
        </div>
      <?php endforeach; ?>
    <?php endif; ?>
  </div>
</main>

<?php include __DIR__ . '/../includes/footer.php'; ?>
