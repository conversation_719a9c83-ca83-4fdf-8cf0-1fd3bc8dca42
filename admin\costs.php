<?php
// /admin/costs.php
require_once __DIR__ . '/../includes/header.php';

// Role-based guard for admin
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
  header('Location: ../auth/login.php');
  exit;
}

// Fetch costs data with error handling
try {
    $costs = $pdo->query("SELECT co.*, ca.name AS category, su.name AS subcategory FROM costs co LEFT JOIN categories ca ON co.category_id = ca.id LEFT JOIN subcategories su ON co.subcategory_id = su.id ORDER BY ca.name, su.name, co.created_at DESC")->fetchAll();
} catch (Exception $e) {
    $costs = [];
}

// Get total number of learners from revenue data
try {
    $totalLearners = $pdo->query("SELECT SUM(total_students) as total FROM revenue")->fetch()['total'] ?? 0;
} catch (Exception $e) {
    $totalLearners = 0;
}

// Group costs by category and subcategory
$groupedCosts = [];
foreach ($costs as $cost) {
    $category = $cost['category'] ?? 'Uncategorized';
    $subcategory = $cost['subcategory'] ?? 'No Subcategory';

    if (!isset($groupedCosts[$category])) {
        $groupedCosts[$category] = [];
    }

    if (!isset($groupedCosts[$category][$subcategory])) {
        $groupedCosts[$category][$subcategory] = [];
    }

    $groupedCosts[$category][$subcategory][] = $cost;
}
?>

  <main class="container">
    <h1 class="section-title"><i class="fas fa-money-bill-wave"></i> Cost Management</h1>

    <div class="card">
      <div class="card-header">
        <h2><i class="fas fa-plus-circle"></i> Add New Cost</h2>
        <div class="learner-info">
          <span class="learner-count">
            <i class="fas fa-users"></i> Total Learners: <strong><?= number_format($totalLearners) ?></strong>
          </span>
        </div>
      </div>
      <form id="cost-form">
        <div class="form-group">
          <label class="form-label" for="cost-description">Description</label>
          <input type="text" id="cost-description" name="description" placeholder="Enter cost description" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="cost-amount">Amount</label>
          <input type="number" id="cost-amount" name="amount" placeholder="0.00" step="0.01" required>
          <small class="form-help" id="amount-help">Enter the base amount per unit</small>
        </div>
        <div class="form-group">
          <label class="form-label" for="category-select">Category</label>
          <select name="category_id" id="category-select" required>
            <option value="">Select Category</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="subcategory-select">Subcategory</label>
          <select name="subcategory_id" id="subcategory-select" required>
            <option value="">Select Category First</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="rate-type">Rate Type</label>
          <select name="rate_type" id="rate-type">
            <option value="daily">Daily</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="num-days">Number of Days</label>
          <input type="number" id="num-days" name="num_days" placeholder="1" min="1" value="1" required>
        </div>

        <!-- Multiply by Learners Checkbox -->
        <div class="form-group">
          <div class="checkbox-group">
            <label class="checkbox-label">
              <input type="checkbox" id="multiply-by-learners" name="multiply_by_learners" value="1">
              <span class="checkmark"></span>
              <span class="checkbox-text">
                <strong>Multiply by all learners</strong>
                <small>Cost will be multiplied by <?= number_format($totalLearners) ?> learners</small>
              </span>
            </label>
          </div>
        </div>

        <!-- Cost Preview -->
        <div class="cost-preview" id="cost-preview" style="display: none;">
          <div class="preview-content">
            <h4><i class="fas fa-calculator"></i> Cost Preview</h4>
            <div class="preview-breakdown">
              <div class="preview-line">
                <span>Base Amount:</span>
                <span id="preview-base">R0.00</span>
              </div>
              <div class="preview-line">
                <span>Days:</span>
                <span id="preview-days">1</span>
              </div>
              <div class="preview-line" id="preview-learners-line" style="display: none;">
                <span>Learners:</span>
                <span id="preview-learners"><?= number_format($totalLearners) ?></span>
              </div>
              <div class="preview-line preview-total">
                <span><strong>Total Cost:</strong></span>
                <span id="preview-total"><strong>R0.00</strong></span>
              </div>
            </div>
          </div>
        </div>

        <button type="submit" class="btn btn-primary">
          <i class="fas fa-plus"></i> Add Cost
        </button>
      </form>
    </div>

    <h2 class="section-subtitle"><i class="fas fa-list"></i> Saved Costs</h2>

    <!-- Categories Dropdown Menu -->
    <div class="saved-costs-dropdown">
      <div class="dropdown-trigger">
        <button class="dropdown-btn" id="categories-dropdown-btn">
          <i class="fas fa-filter"></i> Filter by Category
          <i class="fas fa-chevron-down dropdown-arrow"></i>
        </button>
      </div>
      <div class="dropdown-menu" id="categories-dropdown-menu">
        <div class="dropdown-header">
          <i class="fas fa-bookmark"></i> Saved Costs Categories
        </div>
        <div class="dropdown-category">
          <a href="#" class="dropdown-category-link clear-filters-link" data-action="clear-filters">
            <i class="fas fa-times-circle"></i> Show All Costs
          </a>
        </div>
        <?php
        // Fetch categories and subcategories for dropdown
        try {
          $categoriesQuery = $pdo->query("
            SELECT c.id as category_id, c.name as category_name,
                   s.id as subcategory_id, s.name as subcategory_name
            FROM categories c
            LEFT JOIN subcategories s ON c.id = s.category_id
            ORDER BY c.name, s.name
          ");
          $categoriesData = $categoriesQuery->fetchAll();

          // Group by category
          $dropdownCategories = [];
          foreach ($categoriesData as $row) {
            $categoryName = $row['category_name'];
            if (!isset($dropdownCategories[$categoryName])) {
              $dropdownCategories[$categoryName] = [
                'id' => $row['category_id'],
                'subcategories' => []
              ];
            }
            if ($row['subcategory_id']) {
              $dropdownCategories[$categoryName]['subcategories'][] = [
                'id' => $row['subcategory_id'],
                'name' => $row['subcategory_name']
              ];
            }
          }
        } catch (Exception $e) {
          $dropdownCategories = [];
        }
        ?>

        <?php if (!empty($dropdownCategories)): ?>
          <?php foreach ($dropdownCategories as $categoryName => $categoryData): ?>
            <div class="dropdown-category">
              <a href="#category-<?= $categoryData['id'] ?>" class="dropdown-category-link" data-filter-category="<?= $categoryData['id'] ?>">
                <i class="fas fa-tag"></i> <?= htmlspecialchars($categoryName) ?>
              </a>
              <?php if (!empty($categoryData['subcategories'])): ?>
                <div class="dropdown-subcategories">
                  <?php foreach ($categoryData['subcategories'] as $subcategory): ?>
                    <a href="#subcategory-<?= $subcategory['id'] ?>" class="dropdown-subcategory-link" data-filter-subcategory="<?= $subcategory['id'] ?>">
                      <i class="fas fa-layer-group"></i> <?= htmlspecialchars($subcategory['name']) ?>
                    </a>
                  <?php endforeach; ?>
                </div>
              <?php endif; ?>
            </div>
          <?php endforeach; ?>
        <?php else: ?>
          <div class="dropdown-empty">
            <i class="fas fa-info-circle"></i> No categories found
          </div>
        <?php endif; ?>
      </div>
    </div>

    <?php if (empty($costs)): ?>
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">No Costs Found</h3>
        </div>
        <p class="card-subtitle">Add your first cost using the form above.</p>
      </div>
    <?php else: ?>
      <?php foreach ($groupedCosts as $categoryName => $subcategories): ?>
        <?php
        // Get category ID for navigation
        $categoryId = null;
        foreach ($subcategories as $subcategoryCosts) {
          if (!empty($subcategoryCosts)) {
            $categoryId = $subcategoryCosts[0]['category_id'];
            break;
          }
        }
        ?>
        <div class="card category-costs-section" id="category-<?= $categoryId ?>">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-tag"></i> <?= htmlspecialchars($categoryName) ?>
            </h3>
            <div class="category-summary">
              <?php
                $categoryTotal = 0;
                $categoryCount = 0;
                foreach ($subcategories as $subcategoryCosts) {
                  foreach ($subcategoryCosts as $cost) {
                    $categoryTotal += ($cost['amount'] * $cost['num_days']);
                    $categoryCount++;
                  }
                }
              ?>
              <span class="category-stats">
                <?= $categoryCount ?> costs • R<?= number_format($categoryTotal, 2) ?> total
              </span>
            </div>
          </div>

          <?php foreach ($subcategories as $subcategoryName => $subcategoryCosts): ?>
            <?php
            // Get subcategory ID for navigation
            $subcategoryId = !empty($subcategoryCosts) ? $subcategoryCosts[0]['subcategory_id'] : null;
            ?>
            <div class="subcategory-costs-section" id="subcategory-<?= $subcategoryId ?>">
              <div class="subcategory-header">
                <h3 class="category-name-in-subcategory">
                  <i class="fas fa-tag"></i> <?= htmlspecialchars($categoryName) ?>
                </h3>
                <h4 class="subcategory-title">
                  <i class="fas fa-layer-group"></i> <?= htmlspecialchars($subcategoryName) ?>
                  <span class="subcategory-count">(<?= count($subcategoryCosts) ?> costs)</span>
                </h4>
              </div>

              <div class="table-container">
                <table class="data-table subcategory-table">
                  <thead>
                    <tr>
                      <th>Description</th>
                      <th>Amount</th>
                      <th>Rate Type</th>
                      <th>Days</th>
                      <th>Total</th>
                      <th>Date Added</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <?php foreach ($subcategoryCosts as $cost): ?>
                      <tr>
                        <td>
                          <strong><?= htmlspecialchars($cost['description']) ?></strong>
                        </td>
                        <td>R<?= number_format($cost['amount'], 2) ?></td>
                        <td>
                          <span class="rate-badge rate-<?= $cost['rate_type'] ?>">
                            <?= ucfirst($cost['rate_type']) ?>
                          </span>
                        </td>
                        <td><?= $cost['num_days'] ?></td>
                        <td>
                          <strong>R<?= number_format($cost['amount'] * $cost['num_days'], 2) ?></strong>
                        </td>
                        <td><?= date('M j, Y', strtotime($cost['created_at'])) ?></td>
                        <td>
                          <button class="btn btn-sm btn-secondary edit-btn" data-id="<?= $cost['id'] ?>" title="Edit Cost">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button class="btn btn-sm btn-danger delete-btn" data-id="<?= $cost['id'] ?>" title="Delete Cost">
                            <i class="fas fa-trash"></i>
                          </button>
                        </td>
                      </tr>
                    <?php endforeach; ?>
                  </tbody>
                  <tfoot>
                    <tr class="subcategory-total">
                      <td colspan="4"><strong>Subcategory Total:</strong></td>
                      <td>
                        <strong>
                          R<?php
                            $subcategoryTotal = 0;
                            foreach ($subcategoryCosts as $cost) {
                              $subcategoryTotal += ($cost['amount'] * $cost['num_days']);
                            }
                            echo number_format($subcategoryTotal, 2);
                          ?>
                        </strong>
                      </td>
                      <td colspan="2"></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          <?php endforeach; ?>
        </div>
      <?php endforeach; ?>
    <?php endif; ?>
  </main>

  <!-- Edit Cost Modal -->
  <div id="edit-cost-modal" class="modal" style="display:none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Edit Cost Entry</h3>
        <span class="close-modal">&times;</span>
      </div>
      <form id="edit-cost-form">
        <input type="hidden" name="id" id="edit-cost-id">
        <div class="form-group">
          <label class="form-label" for="edit-cost-description">Description</label>
          <input type="text" name="description" id="edit-cost-description" placeholder="Description" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-cost-amount">Amount</label>
          <input type="number" name="amount" id="edit-cost-amount" placeholder="Amount" step="0.01" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-cost-days">Number of Days</label>
          <input type="number" name="num_days" id="edit-cost-days" placeholder="Days" min="1" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-cost-rate">Rate Type</label>
          <select name="rate_type" id="edit-cost-rate">
            <option value="daily">Daily</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-save"></i> Save Changes
        </button>
      </form>
    </div>
  </div>

  <script src="../assets/js/edit-handlers-new.js"></script>

  <script>
    // Cost calculation and preview functionality
    document.addEventListener('DOMContentLoaded', () => {

      // === DROPDOWN POPULATION ===
      const categorySelect = document.getElementById("category-select");
      const subcategorySelect = document.getElementById("subcategory-select");

      if (categorySelect) {
        console.log("Category select found, loading categories...");
        // Load categories
        fetch("../ajax/get_categories.php")
          .then(res => {
            console.log("Categories response status:", res.status);
            return res.json();
          })
          .then(data => {
            console.log("Categories data received:", data);
            categorySelect.innerHTML = '<option value="">Select Category</option>';

            if (Array.isArray(data)) {
              console.log("Processing", data.length, "categories");
              data.forEach(cat => {
                const opt = document.createElement("option");
                opt.value = cat.id;
                opt.textContent = cat.name;
                categorySelect.appendChild(opt);
                console.log("Added category:", cat.name);
              });
            } else if (data.error) {
              console.error("Server error:", data.error);
              categorySelect.innerHTML = '<option value="">Error loading categories</option>';
            } else {
              console.error("Categories data is not an array:", data);
              categorySelect.innerHTML = '<option value="">Invalid data format</option>';
            }
          })
          .catch(err => {
            console.error("Error loading categories:", err);
            categorySelect.innerHTML = '<option value="">Network error</option>';
          });

        // When category changes, load subcategories
        categorySelect.addEventListener("change", () => {
          const catId = categorySelect.value;
          if (!catId) {
            subcategorySelect.innerHTML = '<option value="">Select Category First</option>';
            return;
          }

          subcategorySelect.innerHTML = '<option>Loading…</option>';
          console.log("Loading subcategories for category:", catId);

          fetch(`../ajax/get_subcategories.php?category_id=${catId}`)
            .then(res => {
              console.log("Subcategories response status:", res.status);
              return res.json();
            })
            .then(data => {
              console.log("Subcategories data received:", data);
              subcategorySelect.innerHTML = '<option value="">Select Subcategory</option>';

              if (data.success && Array.isArray(data.subcategories)) {
                console.log("Processing", data.subcategories.length, "subcategories");
                data.subcategories.forEach(sub => {
                  const opt = document.createElement("option");
                  opt.value = sub.id;
                  opt.textContent = sub.name;
                  subcategorySelect.appendChild(opt);
                  console.log("Added subcategory:", sub.name);
                });

                if (data.subcategories.length === 0) {
                  subcategorySelect.innerHTML = '<option value="">No subcategories available</option>';
                }
              } else {
                console.error("Invalid subcategories data:", data);
                subcategorySelect.innerHTML = '<option value="">Error loading subcategories</option>';
              }
            })
            .catch(err => {
              console.error("Error loading subcategories:", err);
              subcategorySelect.innerHTML = '<option value="">Network error</option>';
            });
        });
      }

      // === FORM SUBMISSION ===
      const costForm = document.getElementById("cost-form");
      if (costForm) {
        costForm.addEventListener("submit", async e => {
          e.preventDefault();
          try {
            const fd = new FormData(costForm);
            const res = await fetch("../ajax/save_cost.php", { method: "POST", body: fd });
            const data = await res.json();

            if (data.success) {
              alert(data.message);
              costForm.reset();
              // Reset dropdowns
              categorySelect.value = "";
              subcategorySelect.innerHTML = '<option value="">Select Category First</option>';
              // Refresh the page to show the updated table
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            } else {
              alert(data.message || "An error occurred");
            }
          } catch (error) {
            console.error("Error saving cost:", error);
            alert("Failed to save cost. Please try again.");
          }
        });
      }
      const amountInput = document.getElementById('cost-amount');
      const daysInput = document.getElementById('num-days');
      const multiplyCheckbox = document.getElementById('multiply-by-learners');
      const costPreview = document.getElementById('cost-preview');
      const previewBase = document.getElementById('preview-base');
      const previewDays = document.getElementById('preview-days');
      const previewLearners = document.getElementById('preview-learners');
      const previewLearnersLine = document.getElementById('preview-learners-line');
      const previewTotal = document.getElementById('preview-total');
      const amountHelp = document.getElementById('amount-help');

      const totalLearners = <?= $totalLearners ?>;

      function updateCostPreview() {
        const amount = parseFloat(amountInput.value) || 0;
        const days = parseInt(daysInput.value) || 1;
        const multiplyByLearners = multiplyCheckbox.checked;

        if (amount > 0) {
          costPreview.style.display = 'block';

          // Update preview values
          previewBase.textContent = 'R' + amount.toLocaleString('en-ZA', { minimumFractionDigits: 2 });
          previewDays.textContent = days;

          // Calculate total
          let total = amount * days;
          if (multiplyByLearners) {
            total *= totalLearners;
            previewLearnersLine.style.display = 'flex';
          } else {
            previewLearnersLine.style.display = 'none';
          }

          previewTotal.textContent = 'R' + total.toLocaleString('en-ZA', { minimumFractionDigits: 2 });
        } else {
          costPreview.style.display = 'none';
        }
      }

      function updateAmountHelp() {
        if (multiplyCheckbox.checked) {
          amountHelp.textContent = `Enter amount per learner (will be multiplied by ${totalLearners.toLocaleString()} learners)`;
          amountHelp.style.color = 'var(--primary)';
          amountHelp.style.fontWeight = '500';
        } else {
          amountHelp.textContent = 'Enter the base amount per unit';
          amountHelp.style.color = 'var(--text-secondary)';
          amountHelp.style.fontWeight = 'normal';
        }
      }

      // Event listeners
      amountInput.addEventListener('input', updateCostPreview);
      daysInput.addEventListener('input', updateCostPreview);
      multiplyCheckbox.addEventListener('change', () => {
        updateCostPreview();
        updateAmountHelp();
      });

      // Initialize
      updateAmountHelp();

      // Update the existing form submission handler to include the calculated amount
      const existingCostForm = document.getElementById('cost-form');
      if (existingCostForm) {
        existingCostForm.addEventListener('submit', (e) => {
          if (multiplyCheckbox.checked) {
            // Add a hidden field with the multiplier info
            const multiplierField = document.createElement('input');
            multiplierField.type = 'hidden';
            multiplierField.name = 'learner_multiplier';
            multiplierField.value = totalLearners;
            existingCostForm.appendChild(multiplierField);

            // Update the amount to reflect the total
            const originalAmount = parseFloat(amountInput.value) || 0;
            const days = parseInt(daysInput.value) || 1;
            const totalAmount = originalAmount * days * totalLearners;

            // Add original amount for reference
            const originalAmountField = document.createElement('input');
            originalAmountField.type = 'hidden';
            originalAmountField.name = 'original_amount';
            originalAmountField.value = originalAmount;
            existingCostForm.appendChild(originalAmountField);

            // Update the main amount field to the calculated total
            amountInput.value = totalAmount;

            // Set days to 1 since we've already calculated the total
            daysInput.value = 1;
          }
        });
      }
    });
  </script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>
