<?php
// /ajax/save_cost.php
session_start();
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';
header('Content-Type: application/json');

// Debug: Log request to detect duplicates
error_log("Cost save request at: " . date('Y-m-d H:i:s') . " - Description: " . ($_POST['description'] ?? 'N/A'));

// Check authentication
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $description = trim($_POST['description'] ?? '');
    $amount = floatval($_POST['amount'] ?? 0);
    $category_id = intval($_POST['category_id'] ?? 0);
    $subcategory_id = intval($_POST['subcategory_id'] ?? 0);
    $rate_type = $_POST['rate_type'] ?? 'daily';
    $num_days = intval($_POST['num_days'] ?? 1);

    // Validate input
    $errors = [];
    if (empty($description)) {
        $errors[] = "Description is required";
    }
    if ($amount <= 0) {
        $errors[] = "Amount must be greater than zero";
    }
    if ($category_id <= 0) {
        $errors[] = "Please select a category";
    }
    if ($subcategory_id <= 0) {
        $errors[] = "Please select a subcategory";
    }
    if (!in_array($rate_type, ['daily', 'monthly'])) {
        $errors[] = "Invalid rate type";
    }
    if ($num_days <= 0) {
        $errors[] = "Number of days must be greater than zero";
    }

    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare("INSERT INTO costs (description, amount, category_id, subcategory_id, rate_type, num_days) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute([$description, $amount, $category_id, $subcategory_id, $rate_type, $num_days]);

            $newId = $pdo->lastInsertId();
            echo json_encode([
                'success' => true,
                'message' => 'Cost added successfully.',
                'id' => $newId
            ]);
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error saving cost: ' . $e->getMessage()
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid input data: ' . implode(', ', $errors)
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
}
?>